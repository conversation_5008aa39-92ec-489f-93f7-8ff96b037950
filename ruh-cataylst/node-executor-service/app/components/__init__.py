# Components package

# Import all components to ensure they are registered
# Note: Some imports may fail due to missing dependencies, which is handled gracefully
try:
    from . import api_component
except ImportError:
    pass  # Skip if aiohttp is not available

from . import combine_text_component
from . import doc_component
from . import select_data_component
from . import split_text_component
from . import text_analysis_component
from . import alter_metadata_component
from . import convert_script_data_component
from . import data_to_dataframe_component
from . import message_to_data_component
from . import conditional_component  # Enabled: ConditionalNode now executes as component

# Import unified AI component executor
try:
    from . import ai_component  # Unified AI component executor
except ImportError as e:
    import logging
    logging.warning(f"Failed to import AI component executor: {e}. AI components may not be available.")
from . import ai_component  # Unified AI component executor
