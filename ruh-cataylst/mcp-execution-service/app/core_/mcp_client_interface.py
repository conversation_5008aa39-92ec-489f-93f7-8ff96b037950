"""
Abstract interface for MCP clients.
Provides a common interface for both MCP SDK and custom JSON-RPC implementations.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional
import logging

logger = logging.getLogger(__name__)


class MCPClientInterface(ABC):
    """
    Abstract base class for MCP client implementations.
    
    This interface provides a common API for both:
    1. Official MCP SDK-based clients
    2. Custom JSON-RPC implementations
    
    The interface supports async context management for proper resource cleanup.
    """

    @abstractmethod
    async def connect(self, config: Dict[str, Any]) -> None:
        """
        Establish connection to the MCP server.
        
        Args:
            config: Connection configuration containing server details,
                   authentication, SSH parameters, etc.
        
        Raises:
            ConnectionError: If connection fails
            AuthenticationError: If authentication fails
        """
        pass

    @abstractmethod
    async def list_tools(self) -> List[Dict[str, Any]]:
        """
        List all available tools from the MCP server.
        
        Returns:
            List of tool definitions with name, description, and parameters
            
        Raises:
            ProtocolError: If the request fails
            ConnectionError: If not connected
        """
        pass

    @abstractmethod
    async def call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        Call a specific tool with the given arguments.
        
        Args:
            tool_name: Name of the tool to call
            arguments: Tool-specific arguments
            
        Returns:
            Tool execution result
            
        Raises:
            ProtocolError: If the tool call fails
            ConnectionError: If not connected
            TimeoutError: If the call times out
        """
        pass

    @abstractmethod
    async def close(self) -> None:
        """
        Close the connection and clean up resources.
        
        This method should be idempotent and safe to call multiple times.
        """
        pass

    @abstractmethod
    async def is_connected(self) -> bool:
        """
        Check if the client is currently connected.
        
        Returns:
            True if connected and ready for operations, False otherwise
        """
        pass

    # Optional methods with default implementations
    async def list_resources(self) -> List[Dict[str, Any]]:
        """
        List available resources (optional MCP feature).
        
        Returns:
            List of resource definitions
            
        Note:
            Default implementation returns empty list.
            Override if the implementation supports resources.
        """
        logger.debug("list_resources not implemented, returning empty list")
        return []

    async def list_prompts(self) -> List[Dict[str, Any]]:
        """
        List available prompts (optional MCP feature).
        
        Returns:
            List of prompt definitions
            
        Note:
            Default implementation returns empty list.
            Override if the implementation supports prompts.
        """
        logger.debug("list_prompts not implemented, returning empty list")
        return []

    async def health_check(self) -> Dict[str, Any]:
        """
        Perform a health check on the connection.
        
        Returns:
            Health status information
            
        Note:
            Default implementation checks connection status.
            Override for more detailed health checks.
        """
        is_connected = await self.is_connected()
        return {
            "status": "healthy" if is_connected else "unhealthy",
            "connected": is_connected,
            "implementation": self.__class__.__name__
        }

    # Async context manager methods
    async def __aenter__(self):
        """Enter async context - connection should already be established."""
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Exit async context - clean up resources."""
        await self.close()


class MCPClientError(Exception):
    """Base exception for MCP client errors."""
    pass


class MCPConnectionError(MCPClientError):
    """Raised when connection operations fail."""
    pass


class MCPProtocolError(MCPClientError):
    """Raised when MCP protocol operations fail."""
    pass


class MCPTimeoutError(MCPClientError):
    """Raised when operations timeout."""
    pass


class MCPAuthenticationError(MCPClientError):
    """Raised when authentication fails."""
    pass
