"""
Factory for creating MCP clients based on configuration.
Provides environment-based switching between MCP SDK and custom implementations.
"""

import logging
from typing import Dict, Any, Optional

from .mcp_client_interface import MC<PERSON>lientInterface, MCPConnectionError
from .mcp_sdk_client import MCPSDKClient
from .mcp_custom_client import MCPCustomClient
from app.config.config import settings

logger = logging.getLogger(__name__)


class MCPClientFactory:
    """
    Factory class for creating MCP client instances.
    
    Handles environment-based selection between:
    1. Official MCP SDK implementation (for better large response handling)
    2. Custom JSON-RPC implementation (fallback/compatibility)
    """

    @staticmethod
    def create_client(
        force_implementation: Optional[str] = None,
        config_override: Optional[Dict[str, Any]] = None
    ) -> MCPClientInterface:
        """
        Create an MCP client based on configuration.
        
        Args:
            force_implementation: Force specific implementation ("sdk" or "custom")
                                If None, uses settings.use_mcp_sdk
            config_override: Override configuration settings for this client
            
        Returns:
            MCPClientInterface implementation
            
        Raises:
            MCPConnectionError: If implementation selection fails
        """
        try:
            # Determine which implementation to use
            use_sdk = MCPClientFactory._should_use_sdk(force_implementation, config_override)
            
            if use_sdk:
                logger.info("🚀 Creating MCP SDK client (official implementation)")
                client = MCPSDKClient()
                implementation = "SDK"
            else:
                logger.info("🔧 Creating custom JSON-RPC client (fallback implementation)")
                client = MCPCustomClient()
                implementation = "Custom"
            
            # Log configuration details
            logger.info(f"✅ Created {implementation} MCP client")
            logger.debug(f"Configuration: USE_MCP_SDK={settings.use_mcp_sdk}, "
                        f"Force={force_implementation}")
            
            return client
            
        except Exception as e:
            logger.error(f"❌ Failed to create MCP client: {e}")
            raise MCPConnectionError(f"Client creation failed: {e}") from e

    @staticmethod
    def create_client_with_fallback(
        config: Dict[str, Any],
        prefer_sdk: bool = True
    ) -> MCPClientInterface:
        """
        Create client with automatic fallback capability.
        
        Tries the preferred implementation first, falls back to the other if it fails.
        This is useful for handling platform-specific issues.
        
        Args:
            config: Connection configuration
            prefer_sdk: If True, try SDK first then custom. If False, reverse order.
            
        Returns:
            MCPClientInterface implementation that successfully connects
            
        Raises:
            MCPConnectionError: If both implementations fail
        """
        implementations = ["sdk", "custom"] if prefer_sdk else ["custom", "sdk"]
        last_error = None
        
        for impl in implementations:
            try:
                logger.info(f"🔄 Attempting {impl.upper()} implementation")
                client = MCPClientFactory.create_client(force_implementation=impl)
                
                # Test the connection
                # Note: We don't call connect() here as that's the caller's responsibility
                # This just creates the client instance
                
                logger.info(f"✅ Successfully created {impl.upper()} client")
                return client
                
            except Exception as e:
                last_error = e
                logger.warning(f"⚠️ {impl.upper()} implementation failed: {e}")
                continue
        
        # Both implementations failed
        error_msg = f"All implementations failed. Last error: {last_error}"
        logger.error(f"❌ {error_msg}")
        raise MCPConnectionError(error_msg)

    @staticmethod
    def _should_use_sdk(
        force_implementation: Optional[str] = None,
        config_override: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        Determine whether to use the MCP SDK implementation.
        
        Args:
            force_implementation: Force specific implementation
            config_override: Configuration overrides
            
        Returns:
            True if should use SDK, False for custom implementation
        """
        # Handle forced implementation
        if force_implementation:
            if force_implementation.lower() == "sdk":
                return True
            elif force_implementation.lower() == "custom":
                return False
            else:
                logger.warning(f"Unknown forced implementation '{force_implementation}', using default")
        
        # Check for config override
        if config_override and "use_mcp_sdk" in config_override:
            return bool(config_override["use_mcp_sdk"])
        
        # Use global settings
        return settings.use_mcp_sdk

    @staticmethod
    def get_implementation_info() -> Dict[str, Any]:
        """
        Get information about available implementations and current configuration.
        
        Returns:
            Dictionary with implementation details
        """
        return {
            "available_implementations": ["sdk", "custom"],
            "current_default": "sdk" if settings.use_mcp_sdk else "custom",
            "configuration": {
                "use_mcp_sdk": settings.use_mcp_sdk,
                "mcp_sdk_timeout": settings.mcp_sdk_timeout,
                "mcp_sdk_buffer_size": settings.mcp_sdk_buffer_size,
            },
            "sdk_benefits": [
                "Better large response handling",
                "Official implementation",
                "Proper streaming support",
                "Enhanced buffer management"
            ],
            "custom_benefits": [
                "Proven compatibility",
                "Windows-tested",
                "Fallback reliability",
                "Direct control over JSON-RPC"
            ]
        }


# Convenience functions for common use cases
def create_mcp_client(config: Optional[Dict[str, Any]] = None) -> MCPClientInterface:
    """
    Convenience function to create an MCP client with default settings.
    
    Args:
        config: Optional configuration overrides
        
    Returns:
        MCPClientInterface implementation
    """
    return MCPClientFactory.create_client(config_override=config)


def create_sdk_client() -> MCPClientInterface:
    """
    Convenience function to force creation of SDK client.
    
    Returns:
        MCPSDKClient instance
    """
    return MCPClientFactory.create_client(force_implementation="sdk")


def create_custom_client() -> MCPClientInterface:
    """
    Convenience function to force creation of custom client.
    
    Returns:
        MCPCustomClient instance
    """
    return MCPClientFactory.create_client(force_implementation="custom")


def create_client_with_fallback(config: Dict[str, Any]) -> MCPClientInterface:
    """
    Convenience function to create client with automatic fallback.
    
    Args:
        config: Connection configuration
        
    Returns:
        MCPClientInterface implementation
    """
    return MCPClientFactory.create_client_with_fallback(config)
