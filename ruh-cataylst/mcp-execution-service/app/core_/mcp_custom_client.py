"""
Custom JSON-RPC client implementation wrapper.
Wraps the existing MCPClient to implement the MCPClientInterface.
"""

import logging
from typing import Dict, Any, List, Optional

from .mcp_client_interface import (
    MCPClientInterface, 
    MCPConnectionError, 
    MCPProtocolError, 
    MCPTimeoutError,
    MCPAuthenticationError
)
from .client import MC<PERSON>lient
from app.schemas.client import ProtocolError, AuthenticationError
from app.services.authentication_manager import AuthenticationManager
from app.schemas.client import ConnectionConfig

logger = logging.getLogger(__name__)


class MCPCustomClient(MCPClientInterface):
    """
    Custom JSON-RPC MCP client implementation.
    
    This wraps the existing MCPClient to provide the standard interface
    while maintaining the current custom JSON-RPC implementation as a fallback.
    """

    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.MCPCustomClient")
        self._client: Optional[MCPClient] = None
        self._connected = False
        self._config: Optional[Dict[str, Any]] = None

    async def connect(self, config: Dict[str, Any]) -> None:
        """
        Connect using the existing custom MCPClient.
        
        Args:
            config: Configuration containing connection parameters
        """
        try:
            self._config = config
            self.logger.info("🔗 Connecting using custom JSON-RPC implementation")
            
            # Extract configuration parameters
            connection_type = config.get("connection_type", "ssh_docker")
            server_url = config.get("server_url")
            docker_image = config.get("container_name", "mcp-server")
            container_command = config.get("container_command")
            headers = config.get("headers", {})
            
            # Create authentication manager if needed
            auth_manager = None
            if config.get("access_token") or config.get("api_key") or headers:
                auth_manager = AuthenticationManager()
                
                if config.get("access_token"):
                    auth_manager.add_bearer_token(config["access_token"])
                
                if config.get("api_key"):
                    auth_manager.add_api_key(
                        api_key=config["api_key"],
                        location=config.get("api_key_location", "header"),
                        key_name=config.get("api_key_name", "X-API-Key")
                    )
                
                if headers:
                    auth_manager.add_custom_auth(headers=headers)
            
            # Create connection config
            connection_config = ConnectionConfig(
                timeout=config.get("timeout", 30),
                max_retries=config.get("max_retries", 3),
                enable_health_check=config.get("enable_health_check", True)
            )
            
            # Create the custom MCPClient
            self._client = MCPClient(
                server_url=server_url,
                connection_type=connection_type,
                headers=headers,
                docker_image=docker_image,
                container_command=container_command,
                auth_manager=auth_manager,
                connection_config=connection_config,
                enable_health_check=config.get("enable_health_check", True),
                credential_service=config.get("credential_service"),
                mcp_config=config.get("mcp_config"),
                use_fallback_ssh=config.get("use_fallback_ssh", True)
            )
            
            # Establish connection using the existing client
            await self._client.__aenter__()
            self._connected = True
            
            self.logger.info("✅ Custom JSON-RPC connection established successfully")
            
        except AuthenticationError as e:
            self.logger.error(f"❌ Authentication failed: {e}")
            await self.close()
            raise MCPAuthenticationError(f"Authentication failed: {e}") from e
        except ProtocolError as e:
            self.logger.error(f"❌ Protocol error: {e}")
            await self.close()
            raise MCPProtocolError(f"Protocol error: {e}") from e
        except Exception as e:
            self.logger.error(f"❌ Failed to connect using custom client: {e}")
            await self.close()
            raise MCPConnectionError(f"Custom connection failed: {e}") from e

    async def list_tools(self) -> List[Dict[str, Any]]:
        """List tools using the custom client."""
        if not self._client or not self._connected:
            raise MCPConnectionError("Not connected - call connect() first")
        
        try:
            self.logger.debug("📋 Listing tools via custom JSON-RPC")
            
            # Use the existing client's list_tools method
            tools_result = await self._client.list_tools()
            
            # Convert to consistent dict format
            tools_list = []
            for tool in tools_result:
                if hasattr(tool, 'model_dump'):
                    tools_list.append(tool.model_dump())
                elif hasattr(tool, '__dict__'):
                    tools_list.append(tool.__dict__)
                elif hasattr(tool, 'name'):
                    # Handle MCP types.Tool objects
                    tool_dict = {
                        'name': tool.name,
                        'description': getattr(tool, 'description', ''),
                    }
                    if hasattr(tool, 'inputSchema'):
                        tool_dict['inputSchema'] = tool.inputSchema
                    tools_list.append(tool_dict)
                else:
                    tools_list.append(tool)
            
            self.logger.info(f"✅ Retrieved {len(tools_list)} tools via custom client")
            return tools_list
            
        except ProtocolError as e:
            self.logger.error(f"❌ Failed to list tools via custom client: {e}")
            raise MCPProtocolError(f"Failed to list tools: {e}") from e
        except Exception as e:
            self.logger.error(f"❌ Unexpected error listing tools: {e}")
            raise MCPProtocolError(f"Failed to list tools: {e}") from e

    async def call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Call tool using the custom client."""
        if not self._client or not self._connected:
            raise MCPConnectionError("Not connected - call connect() first")
        
        try:
            self.logger.debug(f"🔧 Calling tool '{tool_name}' via custom JSON-RPC")
            
            # Use the existing client's call_tool method
            result = await self._client.call_tool(tool_name, arguments)
            
            # Convert result to dict format
            if hasattr(result, 'model_dump'):
                result_dict = result.model_dump()
            elif hasattr(result, '__dict__'):
                result_dict = result.__dict__
            elif hasattr(result, 'content'):
                # Handle MCP types.CallToolResult objects
                result_dict = {
                    'content': result.content,
                }
                if hasattr(result, 'isError'):
                    result_dict['isError'] = result.isError
            else:
                result_dict = result
            
            self.logger.info(f"✅ Tool '{tool_name}' executed successfully via custom client")
            return result_dict
            
        except ProtocolError as e:
            self.logger.error(f"❌ Failed to call tool '{tool_name}' via custom client: {e}")
            raise MCPProtocolError(f"Failed to call tool {tool_name}: {e}") from e
        except TimeoutError as e:
            self.logger.error(f"❌ Tool call '{tool_name}' timed out: {e}")
            raise MCPTimeoutError(f"Tool call {tool_name} timed out: {e}") from e
        except Exception as e:
            self.logger.error(f"❌ Unexpected error calling tool '{tool_name}': {e}")
            raise MCPProtocolError(f"Failed to call tool {tool_name}: {e}") from e

    async def close(self) -> None:
        """Close connection and clean up resources."""
        self.logger.info("🔒 Closing custom JSON-RPC client")
        
        try:
            if self._client:
                try:
                    await self._client.__aexit__(None, None, None)
                except Exception as e:
                    self.logger.warning(f"Error closing custom client: {e}")
                self._client = None
            
            self._connected = False
            self.logger.info("✅ Custom JSON-RPC client closed successfully")
            
        except Exception as e:
            self.logger.error(f"❌ Error during custom client cleanup: {e}")

    async def is_connected(self) -> bool:
        """Check if client is connected."""
        if not self._connected or not self._client:
            return False
        
        # Use the existing client's connection check if available
        if hasattr(self._client, '_session') and self._client._session:
            return True
        
        return self._connected

    async def health_check(self) -> Dict[str, Any]:
        """Perform health check using the custom client."""
        base_health = await super().health_check()
        
        # Add custom client specific health info
        base_health.update({
            "implementation_type": "CUSTOM_JSON_RPC",
            "fallback_ssh": True,
            "proven_compatibility": True
        })
        
        if self._client and hasattr(self._client, 'enable_health_check'):
            base_health['health_check_enabled'] = self._client.enable_health_check
        
        if self._config:
            base_health['connection_type'] = self._config.get('connection_type', 'unknown')
        
        return base_health
