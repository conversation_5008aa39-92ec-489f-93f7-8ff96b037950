#!/usr/bin/env python3
"""
Test script for Phase 2: MCP client abstraction layer.
Tests the interface, implementations, and factory.
"""

import os
import sys
import asyncio
import logging

# Add the app directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

def test_interface_import():
    """Test if the interface can be imported."""
    try:
        from app.core_.mcp_client_interface import (
            MCPClientInterface,
            MCPClientError,
            MCPConnectionError,
            MCPProtocolError,
            MCPTimeoutError,
            MCPAuthenticationError
        )
        print("✅ Interface imported successfully:")
        print("  - MCPClientInterface")
        print("  - Exception classes")
        return True
    except ImportError as e:
        print(f"❌ Failed to import interface: {e}")
        return False

def test_implementations_import():
    """Test if implementations can be imported."""
    try:
        from app.core_.mcp_sdk_client import MCPSDKClient
        from app.core_.mcp_custom_client import MCPCustomClient
        print("✅ Implementations imported successfully:")
        print("  - MCPSDKClient")
        print("  - MCPCustomClient")
        return True
    except ImportError as e:
        print(f"❌ Failed to import implementations: {e}")
        return False

def test_factory_import():
    """Test if factory can be imported."""
    try:
        from app.core_.mcp_client_factory import (
            MCPClientFactory,
            create_mcp_client,
            create_sdk_client,
            create_custom_client,
            create_client_with_fallback
        )
        print("✅ Factory imported successfully:")
        print("  - MCPClientFactory")
        print("  - Convenience functions")
        return True
    except ImportError as e:
        print(f"❌ Failed to import factory: {e}")
        return False

def test_interface_compliance():
    """Test if implementations comply with the interface."""
    try:
        from app.core_.mcp_client_interface import MCPClientInterface
        from app.core_.mcp_sdk_client import MCPSDKClient
        from app.core_.mcp_custom_client import MCPCustomClient
        
        # Check if implementations inherit from interface
        sdk_client = MCPSDKClient()
        custom_client = MCPCustomClient()
        
        assert isinstance(sdk_client, MCPClientInterface), "MCPSDKClient must implement MCPClientInterface"
        assert isinstance(custom_client, MCPClientInterface), "MCPCustomClient must implement MCPClientInterface"
        
        # Check required methods exist
        required_methods = ['connect', 'list_tools', 'call_tool', 'close', 'is_connected']
        
        for method in required_methods:
            assert hasattr(sdk_client, method), f"MCPSDKClient missing method: {method}"
            assert hasattr(custom_client, method), f"MCPCustomClient missing method: {method}"
            assert callable(getattr(sdk_client, method)), f"MCPSDKClient.{method} not callable"
            assert callable(getattr(custom_client, method)), f"MCPCustomClient.{method} not callable"
        
        print("✅ Interface compliance verified:")
        print("  - Both implementations inherit from MCPClientInterface")
        print("  - All required methods present and callable")
        return True
        
    except Exception as e:
        print(f"❌ Interface compliance test failed: {e}")
        return False

def test_factory_creation():
    """Test factory client creation."""
    try:
        from app.core_.mcp_client_factory import MCPClientFactory
        from app.core_.mcp_client_interface import MCPClientInterface
        
        # Test default creation
        client1 = MCPClientFactory.create_client()
        assert isinstance(client1, MCPClientInterface), "Factory must return MCPClientInterface"
        
        # Test forced SDK creation
        client2 = MCPClientFactory.create_client(force_implementation="sdk")
        assert client2.__class__.__name__ == "MCPSDKClient", "Should create SDK client when forced"
        
        # Test forced custom creation
        client3 = MCPClientFactory.create_client(force_implementation="custom")
        assert client3.__class__.__name__ == "MCPCustomClient", "Should create custom client when forced"
        
        print("✅ Factory creation tests passed:")
        print(f"  - Default client: {client1.__class__.__name__}")
        print(f"  - Forced SDK client: {client2.__class__.__name__}")
        print(f"  - Forced custom client: {client3.__class__.__name__}")
        return True
        
    except Exception as e:
        print(f"❌ Factory creation test failed: {e}")
        return False

async def test_sdk_client_stub():
    """Test SDK client stub functionality."""
    try:
        from app.core_.mcp_sdk_client import MCPSDKClient
        
        client = MCPSDKClient()
        
        # Test connection
        await client.connect({"connection_type": "ssh_docker"})
        assert await client.is_connected(), "Client should be connected after connect()"
        
        # Test list_tools
        tools = await client.list_tools()
        assert isinstance(tools, list), "list_tools should return a list"
        assert len(tools) > 0, "Should return at least one mock tool"
        
        # Test call_tool
        result = await client.call_tool("test_tool", {"message": "test"})
        assert isinstance(result, dict), "call_tool should return a dict"
        assert "content" in result, "Result should have content"
        
        # Test health check
        health = await client.health_check()
        assert health["implementation"] == "MCPSDKClient", "Health check should identify implementation"
        
        # Test close
        await client.close()
        assert not await client.is_connected(), "Client should be disconnected after close()"
        
        print("✅ SDK client stub functionality verified")
        return True
        
    except Exception as e:
        print(f"❌ SDK client stub test failed: {e}")
        return False

def main():
    """Run all Phase 2 tests."""
    print("🚀 Testing Phase 2: MCP Client Abstraction Layer")
    print("=" * 60)
    
    # Configure logging
    logging.basicConfig(level=logging.WARNING)  # Reduce noise during testing
    
    tests = [
        ("Interface Import", test_interface_import),
        ("Implementations Import", test_implementations_import),
        ("Factory Import", test_factory_import),
        ("Interface Compliance", test_interface_compliance),
        ("Factory Creation", test_factory_creation),
    ]
    
    # Async tests
    async_tests = [
        ("SDK Client Stub", test_sdk_client_stub),
    ]
    
    results = []
    
    # Run synchronous tests
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}:")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Run asynchronous tests
    for test_name, test_func in async_tests:
        print(f"\n📋 {test_name}:")
        try:
            result = asyncio.run(test_func())
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Phase 2 Test Summary:")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status}: {test_name}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 Phase 2 complete! Abstraction layer is ready.")
        print("\n🔄 Ready for Phase 3: Environment-Based Client Factory")
    else:
        print("⚠️  Some tests failed. Please check the issues above.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
